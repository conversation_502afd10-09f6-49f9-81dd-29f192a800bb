<!-- pages/complaint/index.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">{{isEditMode ? '编辑建议反馈' : '建议反馈'}}</text>
    <view class="header-actions" wx:if="{{!isEditMode}}">
      <text class="history-btn" bindtap="viewHistory">历史记录</text>
    </view>
  </view>

  <!-- 子分类选择 -->
  <view class="subcategory-section">
    <view class="section-title">
      <text>建议类型</text>
    </view>
    <view class="subcategory-container">
      <view
        wx:for="{{subCategoryOptions}}"
        wx:key="value"
        class="subcategory-item {{subCategory === item.value ? 'active' : ''}}"
        bindtap="selectSubCategory"
        data-subcategory="{{item.value}}"
      >
        <view class="subcategory-content">
          <text class="subcategory-label">{{item.label}}</text>
          <text class="subcategory-desc">{{item.desc}}</text>
        </view>
        <view class="subcategory-check" wx:if="{{subCategory === item.value}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 标题输入 -->
  <view class="title-section">
    <view class="section-title">
      <text>标题</text>
      <text class="required">*</text>
    </view>
    <input 
      class="title-input" 
      placeholder="请输入标题"
      value="{{title}}"
      bindinput="onTitleInput"
      maxlength="{{titleMaxLength}}"
    />
    <view class="char-count">{{title.length}}/{{titleMaxLength}}</view>
  </view>

  <!-- 内容输入 -->
  <view class="content-section">
    <view class="section-title">
      <text>详细描述</text>
      <text class="required">*</text>
    </view>
    <textarea
      class="content-input"
      placeholder="{{placeholders[subCategory] || '请详细描述您的建议'}}"
      value="{{content}}"
      bindinput="onContentInput"
      maxlength="{{contentMaxLength}}"
      auto-height
    ></textarea>
    <view class="char-count">{{content.length}}/{{contentMaxLength}}</view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-title">
      <text>联系方式</text>
      <text class="optional-text">（可选）</text>
    </view>
    <input
      class="contact-input {{contactError ? 'error' : ''}}"
      placeholder="请输入手机号码（可选）"
      value="{{contactInfo}}"
      bindinput="onContactInput"
      type="number"
    />
    <view class="error-tip" wx:if="{{contactError}}">
      <text class="error-text">{{contactError}}</text>
    </view>
  </view>

  <!-- 图片上传 -->
  <view class="photo-section">
    <view class="section-title">
      <text>上传图片</text>
      <text class="optional-text">（可选）</text>
    </view>
    <view class="photo-container">
      <!-- 已上传的图片 -->
      <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
        <image src="{{item}}" class="photo-preview" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
        <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
          <text class="delete-icon">×</text>
        </view>
      </view>
      
      <!-- 上传按钮 -->
      <view wx:if="{{photoList.length < 6}}" class="photo-upload" bindtap="chooseImage">
        <image class="upload-image" src="//xian7.zos.ctyun.cn/pet/static/upload-image.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="photo-tip">最多可上传6张图片</view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="submitSuggestion" disabled="{{!canSubmit}}">
      {{isEditMode ? '更新建议' : '提交建议'}}
    </button>
  </view>
</view>



<!-- 加载提示 -->
<view class="loading-mask" wx:if="{{isSubmitting}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">提交中...</text>
  </view>
</view>
